from PIL import Image
import os
import zipfile

def prepare_training_images(input_folder, output_zip):
    with zipfile.ZipFile(output_zip, 'w') as zipf:
        images = sorted([f for f in os.listdir(input_folder) if f.lower().endswith(('.png', '.jpg', '.jpeg'))])
        
        for i, filename in enumerate(images):
            # Open and resize image
            img = Image.open(os.path.join(input_folder, filename))
            img = img.convert('RGB')
            
            # Center crop to square
            width, height = img.size
            size = min(width, height)
            left = (width - size) // 2
            top = (height - size) // 2
            img = img.crop((left, top, left + size, top + size))
            
            # Resize to 1024x1024
            img = img.resize((1024, 1024), Image.Resampling.LANCZOS)
            
            # Save to zip
            output_name = f'image_{i+1:03d}.png'
            img.save(output_name, 'PNG')
            zipf.write(output_name)
            os.remove(output_name)
    
    print(f"Created {output_zip} with {len(images)} images")

# Run it
prepare_training_images('your_images_folder', 'training_images.zip')