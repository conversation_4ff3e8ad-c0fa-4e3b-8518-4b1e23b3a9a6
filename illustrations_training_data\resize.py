from PIL import Image
import os
import zipfile
import tempfile

def prepare_training_images(input_folder, output_zip):
    # Get all image files
    images = sorted([f for f in os.listdir(input_folder)
                    if f.lower().endswith(('.png', '.jpg', '.jpeg')) and f != 'resize.py'])

    if not images:
        print("No image files found in the directory!")
        return

    print(f"Found {len(images)} images to process...")

    with zipfile.ZipFile(output_zip, 'w', zipfile.ZIP_DEFLATED) as zipf:
        with tempfile.TemporaryDirectory() as temp_dir:
            for i, filename in enumerate(images):
                print(f"Processing {i+1}/{len(images)}: {filename}")

                # Open and resize image
                img_path = os.path.join(input_folder, filename)
                img = Image.open(img_path)
                img = img.convert('RGB')

                # Center crop to square
                width, height = img.size
                size = min(width, height)
                left = (width - size) // 2
                top = (height - size) // 2
                img = img.crop((left, top, left + size, top + size))

                # Resize to 1024x1024
                img = img.resize((1024, 1024), Image.Resampling.LANCZOS)

                # Save to temporary file then add to zip
                output_name = f'image_{i+1:03d}.png'
                temp_path = os.path.join(temp_dir, output_name)
                img.save(temp_path, 'PNG')
                zipf.write(temp_path, output_name)

    print(f"✅ Successfully created {output_zip} with {len(images)} resized images!")

# Run it - process all images in current directory
if __name__ == "__main__":
    current_dir = os.path.dirname(os.path.abspath(__file__))
    output_zip = os.path.join(current_dir, 'resized_images.zip')
    prepare_training_images(current_dir, output_zip)