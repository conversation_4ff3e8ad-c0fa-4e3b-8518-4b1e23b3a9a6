import { fal } from "@fal-ai/client";

# // Set your API key
fal.config({ credentials: "9e07f2be-0213-4c57-a0bf-336c44302ba5:99e3349776c23e154957ac27603738f7" });

async function trainWatercolorLoRA() {
  // First, upload your ZIP file
  const zipFile = // your ZIP file (from file input or fetch)
  const dataUrl = await fal.storage.upload(zipFile);
  
  console.log("Training started...");
  
  const result = await fal.subscribe("fal-ai/flux-lora-fast-training", {
    input: {
      images_data_url: dataUrl,
      trigger_word: "wtrcl style",    // Your unique trigger
      is_style: true,                 // Important for style training!
      steps: 1500,                    // Good for 28 images
      resolution: 1024
    },
    logs: true,
    onQueueUpdate: (update) => {
      console.log("Status:", update.status);
    }
  });
  
  console.log("✅ Training complete!");
  console.log("Your LoRA URL:", result.data.diffusers_lora_file.url);
  console.log("SAVE THIS URL - you'll need it to use your model!");
  
  return result.data.diffusers_lora_file.url;
}

#  Run the training
trainWatercolorLoRA();