import { fal } from "@fal-ai/client";
import fs from 'fs';
import path from 'path';

// Set your API key
fal.config({ credentials: "9e07f2be-0213-4c57-a0bf-336c44302ba5:99e3349776c23e154957ac27603738f7" });
import { fal } from "@fal-ai/client";
import fs from 'fs';
import path from 'path';

// Set your API key
fal.config({ credentials: "YOUR_API_KEY" }); // Note: Remove your API key from public code!

async function trainWatercolorLoRA() {
  try {
    // Load the resized images ZIP file
    const zipPath = path.join(process.cwd(), 'resized_images.zip');

    if (!fs.existsSync(zipPath)) {
      throw new Error(`ZIP file not found at: ${zipPath}`);
    }

    console.log(`📁 Found ZIP file: ${zipPath}`);
    console.log(`📊 File size: ${(fs.statSync(zipPath).size / 1024 / 1024).toFixed(2)} MB`);

    // Read the ZIP file as a buffer
    const zipBuffer = fs.readFileSync(zipPath);
    
    // Create a Blob with the correct MIME type
    const zipBlob = new Blob([zipBuffer], { type: 'application/zip' });
    
    // Create a File object with proper name and type
    const zipFile = new File([zipBlob], 'training_images.zip', {
      type: 'application/zip',
      lastModified: Date.now()
    });

    console.log("⬆️ Uploading ZIP file to Fal AI...");
    const dataUrl = await fal.storage.upload(zipFile);
    console.log("✅ Upload complete!");
    console.log("📎 Upload URL:", dataUrl);

    console.log("🚀 Starting LoRA training...");

    const result = await fal.subscribe("fal-ai/flux-lora-fast-training", {
      input: {
        images_data_url: dataUrl,
        trigger_word: "wtrcl style",
        is_style: true,
        steps: 1500,
        resolution: 1024
      },
      logs: true,
      onQueueUpdate: (update) => {
        console.log("📊 Status:", update.status);
        if (update.logs) {
          update.logs.forEach(log => console.log("📝", log.message));
        }
      }
    });

    console.log("🎉 Training complete!");
    console.log("🔗 Your LoRA URL:", result.data.diffusers_lora_file.url);
    console.log("💾 SAVE THIS URL - you'll need it to use your model!");

    // Save the URL to a file for safekeeping
    fs.writeFileSync('lora_model_url.txt', result.data.diffusers_lora_file.url);
    console.log("📄 URL saved to lora_model_url.txt");

    return result.data.diffusers_lora_file.url;

  } catch (error) {
    console.error("❌ Error:", error.message);
    if (error.body?.detail) {
      console.error("📋 Error details:", JSON.stringify(error.body.detail, null, 2));
    }
    throw error;
  }
}

// If File constructor is not available in Node.js, use this alternative approach:
async function trainWatercolorLoRAAlternative() {
  try {
    const { FormData } = await import('undici');
    
    const zipPath = path.join(process.cwd(), 'resized_images.zip');
    
    if (!fs.existsSync(zipPath)) {
      throw new Error(`ZIP file not found at: ${zipPath}`);
    }

    console.log(`📁 Found ZIP file: ${zipPath}`);
    
    // Create form data
    const formData = new FormData();
    const zipBuffer = fs.readFileSync(zipPath);
    
    formData.append('file', new Blob([zipBuffer], { type: 'application/zip' }), 'training_images.zip');
    
    console.log("⬆️ Uploading ZIP file to Fal AI...");
    const dataUrl = await fal.storage.upload(formData);
    console.log("✅ Upload complete!");

    // Rest of the training code remains the same...
  } catch (error) {
    console.error("❌ Error:", error);
    throw error;
  }
}

// Run the training
trainWatercolorLoRA()
  .then(url => {
    console.log("🏁 Training finished successfully!");
    console.log("🔗 Model URL:", url);
  })
  .catch(error => {
    console.error("💥 Training failed:", error);
    
    // If the first approach fails, try the alternative
    console.log("🔄 Trying alternative upload method...");
    trainWatercolorLoRAAlternative()
      .then(url => {
        console.log("🏁 Training finished successfully with alternative method!");
        console.log("🔗 Model URL:", url);
      })
      .catch(altError => {
        console.error("💥 Alternative method also failed:", altError);
        process.exit(1);
      });
  });