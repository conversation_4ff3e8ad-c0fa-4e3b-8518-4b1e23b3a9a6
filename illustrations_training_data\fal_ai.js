import { fal } from "@fal-ai/client";
import fs from 'fs';
import path from 'path';

// Set your API key
fal.config({ credentials: "9e07f2be-0213-4c57-a0bf-336c44302ba5:99e3349776c23e154957ac27603738f7" });

async function trainWatercolorLoRA() {
  try {
    // Load the resized images ZIP file
    const zipPath = path.join(process.cwd(), 'resized_images.zip');
    
    if (!fs.existsSync(zipPath)) {
      throw new Error(`ZIP file not found at: ${zipPath}`);
    }
    
    console.log(`📁 Found ZIP file: ${zipPath}`);
    console.log(`📊 File size: ${(fs.statSync(zipPath).size / 1024 / 1024).toFixed(2)} MB`);
    
    // Read the ZIP file
    const zipBuffer = fs.readFileSync(zipPath);

    console.log("⬆️ Uploading ZIP file to Fal AI...");
    // Try uploading with explicit content type
    const dataUrl = await fal.storage.upload(zipBuffer, {
      contentType: 'application/zip',
      fileName: 'resized_images.zip'
    });
    console.log("✅ Upload complete!");
    
    console.log("🚀 Starting LoRA training...");
    
    const result = await fal.subscribe("fal-ai/flux-lora-fast-training", {
      input: {
        images_data_url: dataUrl,
        trigger_word: "WTRCL",          // Simplified trigger word
        steps: 1000,                    // Reduced steps for faster training
        resolution: 1024,
        learning_rate: 0.0004,          // Standard learning rate
        batch_size: 1                   // Conservative batch size
      },
      logs: true,
      onQueueUpdate: (update) => {
        console.log("📊 Status:", update.status);
        if (update.logs) {
          update.logs.forEach(log => console.log("📝", log.message));
        }
      }
    });
    
    console.log("🎉 Training complete!");
    console.log("🔗 Your LoRA URL:", result.data.diffusers_lora_file.url);
    console.log("💾 SAVE THIS URL - you'll need it to use your model!");
    
    return result.data.diffusers_lora_file.url;
    
  } catch (error) {
    console.error("❌ Error:", error.message);
    if (error.body && error.body.detail) {
      console.error("📋 Error details:", JSON.stringify(error.body.detail, null, 2));
    }
    throw error;
  }
}

// Run the training
trainWatercolorLoRA()
  .then(url => {
    console.log("🏁 Training finished successfully!");
    console.log("🔗 Model URL:", url);
  })
  .catch(error => {
    console.error("💥 Training failed:", error);
    process.exit(1);
  });
